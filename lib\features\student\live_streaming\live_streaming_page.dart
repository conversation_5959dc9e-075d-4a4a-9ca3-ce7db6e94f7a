import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class LiveStreamingScreen extends StatefulWidget {
  const LiveStreamingScreen({super.key});

  @override
  State<LiveStreamingScreen> createState() => _LiveStreamingScreenState();
}

class _LiveStreamingScreenState extends State<LiveStreamingScreen> {
  // State flags
  bool isLoading = false;
  bool isJoining = false;
  bool isSendingMessage = false;
  bool isViewingStream = false;
  bool isChatOpen = false;
  int unreadMessages = 0;

  // Manual join field
  final TextEditingController _sessionIdController = TextEditingController();

  // Stream data
  List<Map<String, dynamic>> activeStreams = [];
  Map<String, dynamic>? currentStream;
  List<Map<String, dynamic>> chatMessages = [];
  Timer? _chatTimer;
  Timer? _streamsTimer;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadActiveStreams();
    _startPeriodicUpdates();
  }

  @override
  void dispose() {
    _chatTimer?.cancel();
    _streamsTimer?.cancel();
    _sessionIdController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _startPeriodicUpdates() {
    _streamsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _loadActiveStreams();
    });
  }

  Future<void> _loadActiveStreams() async {
    if (!mounted) return;
    
    setState(() {
      isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('https://api.sasthra.com/api/live-streams/active'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (mounted) {
          setState(() {
            activeStreams = List<Map<String, dynamic>>.from(data['streams'] ?? []);
            isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to load streams: ${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading streams: $e')),
        );
      }
    }
  }

  Future<void> _joinStreamBySessionId() async {
    final sessionId = _sessionIdController.text.trim();
    if (sessionId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a session ID')),
      );
      return;
    }

    setState(() {
      isJoining = true;
    });

    try {
      // Simulate joining a stream
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          isJoining = false;
          isViewingStream = true;
          currentStream = {
            'id': sessionId,
            'title': 'Manual Session: $sessionId',
            'instructor': 'Unknown Instructor',
            'viewers': 1,
          };
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Joined session: $sessionId')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isJoining = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to join session: $e')),
        );
      }
    }
  }

  void _leaveStream() {
    setState(() {
      isViewingStream = false;
      currentStream = null;
      isChatOpen = false;
      chatMessages.clear();
    });
    _chatTimer?.cancel();
  }

  Widget _buildStreamViewer() {
    return Scaffold(
      appBar: AppBar(
        title: Text(currentStream?['title'] ?? 'Live Stream'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _leaveStream,
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.chat),
                onPressed: () {
                  setState(() {
                    isChatOpen = !isChatOpen;
                    if (isChatOpen) {
                      unreadMessages = 0;
                    }
                  });
                },
              ),
              if (unreadMessages > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$unreadMessages',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Row(
        children: [
          Expanded(
            flex: isChatOpen ? 3 : 1,
            child: Container(
              color: Colors.black,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.videocam_off,
                      size: 64,
                      color: Colors.white54,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Video stream will appear here',
                      style: TextStyle(
                        color: Colors.white54,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'LiveKit integration temporarily disabled',
                      style: TextStyle(
                        color: Colors.white38,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isChatOpen)
            Container(
              width: 300,
              decoration: const BoxDecoration(
                border: Border(left: BorderSide(color: Colors.grey)),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey)),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.chat),
                        SizedBox(width: 8),
                        Text(
                          'Live Chat',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Expanded(
                    child: Center(
                      child: Text(
                        'Chat functionality temporarily disabled',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            decoration: const InputDecoration(
                              hintText: 'Type a message...',
                              border: OutlineInputBorder(),
                            ),
                            enabled: false,
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.send),
                          onPressed: null,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isViewingStream) {
      return _buildStreamViewer();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streaming'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: RefreshIndicator(
        onRefresh: _loadActiveStreams,
        child: CustomScrollView(
          slivers: [
            // Manual Join Section
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Join Stream Manually',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _sessionIdController,
                            decoration: const InputDecoration(
                              labelText: 'Session ID',
                              hintText: 'Enter session ID',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: isJoining ? null : _joinStreamBySessionId,
                          child: isJoining
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('Join'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Active Streams Header
            const SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverToBoxAdapter(
                child: Text(
                  'Active Streams',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),

            // Loading or Streams List
            if (isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (activeStreams.isEmpty)
              const SliverFillRemaining(
                child: Center(
                  child: Text(
                    'No active streams available',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final stream = activeStreams[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Icon(Icons.live_tv, color: Colors.white),
                          ),
                          title: Text(stream['title'] ?? 'Untitled Stream'),
                          subtitle: Text(
                            'Instructor: ${stream['instructor'] ?? 'Unknown'}\n'
                            'Viewers: ${stream['viewers'] ?? 0}',
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // Simulate joining stream
                            setState(() {
                              isViewingStream = true;
                              currentStream = stream;
                            });
                          },
                        ),
                      );
                    },
                    childCount: activeStreams.length,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
