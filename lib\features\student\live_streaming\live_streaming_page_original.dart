import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
// import 'package:livekit_client/livekit_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class LiveStreamingScreen extends StatefulWidget {
  const LiveStreamingScreen({super.key});

  @override
  State<LiveStreamingScreen> createState() => _LiveStreamingScreenState();
}

class _LiveStreamingScreenState extends State<LiveStreamingScreen> {
  // State flags
  bool isLoading = false;
  bool isJoining = false;
  bool isSendingMessage = false;
  bool isViewingStream = false;
  bool isChatOpen = false;
  int unreadMessages = 0;

  // Manual join field
  final TextEditingController _sessionIdController = TextEditingController();

  // Stream data
  List<ActiveStream> activeStreams = [];
  ActiveStream? currentStream;
  Room? livekitRoom;
  bool livekitConnected = false;
  List<Participant> participants = [];
  List<VideoTrack> screenShareTracks = [];
  List<VideoTrack> cameraTracks = [];

  // Chat
  List<ChatMessage> chatMessages = [];
  final TextEditingController _messageController = TextEditingController();
  Timer? _chatPollTimer;

  // Formatters
  final DateFormat _timeFormat = DateFormat.Hms();

  @override
  void initState() {
    super.initState();
    _loadActiveStreams();
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!isViewingStream) {
        _loadActiveStreams();
      }
    });
  }

  @override
  void dispose() {
    _sessionIdController.dispose();
    _messageController.dispose();
    _chatPollTimer?.cancel();
    _disconnectLiveKit();
    super.dispose();
  }

  Future<void> _loadActiveStreams() async {
    if (isLoading) return;
    setState(() {
      isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse('https://testing.sasthra.in/active-streams'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          activeStreams = (data['active_streams'] as List)
              .map((s) => ActiveStream.fromJson(s))
              .toList();
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load active streams')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _joinStreamById(String sessionId) async {
    if (sessionId.isEmpty || isJoining) return;

    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString('userId');
    final userName = prefs.getString('name') ?? 'Student';
    final token = prefs.getString('token');

    if (userId == null || token == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login first')),
      );
      return;
    }

    setState(() {
      isJoining = true;
    });

    try {
      final requestBody = json.encode({
        'session_id': sessionId,
        'user_id': userId,
        'user_name': userName,
        'user_role': 'student',
      });

      final response = await http.post(
        Uri.parse('https://testing.sasthra.in/api/livekit/join'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final livekitToken = data['livekit_token'];
        final livekitUrl = data['livekit_url'];
        final resolvedSessionId = data['session_id'] ?? sessionId;

        if (livekitToken == null || livekitUrl == null) {
          throw Exception('Missing LiveKit credentials');
        }

        setState(() {
          currentStream = ActiveStream(
            sessionId: resolvedSessionId,
            teacherId: 'Unknown',
            viewerCount: 0,
            uptime: 0,
            quality: 'HD',
          );
          isViewingStream = true;
        });

        await _connectToLiveKit(livekitUrl, livekitToken);
        _startChatPolling(resolvedSessionId);
      } else {
        final error = json.decode(response.body)['message'] ?? 'Unknown error';
        throw Exception(error);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Join failed: $e')),
      );
    } finally {
      setState(() {
        isJoining = false;
      });
    }
  }

  Future<void> _connectToLiveKit(String url, String token) async {
    try {
      final room = Room();
      await room.connect(
        url,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
        ),
      );

      room.localParticipant?.setMicrophoneEnabled(false);
      room.localParticipant?.setCameraEnabled(false);

      // Listen to Room events
      room.events.listen((event) {
        if (event is RoomConnectedEvent) {
          setState(() {
            livekitConnected = true;
          });
        } else if (event is RoomDisconnectedEvent) {
          _onLeaveStream();
        } else if (event is ParticipantConnectedEvent) {
          setState(() {
            participants.add(event.participant);
          });
          _setupTrackListeners(event.participant);
        } else if (event is ParticipantDisconnectedEvent) {
          setState(() {
            participants.remove(event.participant);
          });
        }
      });

      setState(() {
        livekitRoom = room;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Connection failed: $e')),
      );
      setState(() {
        isViewingStream = false;
      });
    }
  }

  void _setupTrackListeners(Participant participant) {
    participant.events.listen((event) {
      if (event is TrackSubscribedEvent) {
        final track = event.track;
        final publication = event.publication;
        if (track is VideoTrack) {
          setState(() {
            if (publication.source == TrackSource.screenShareVideo ||
                publication.source == TrackSource.screenShareAudio) {
              screenShareTracks.add(track);
            } else {
              cameraTracks.add(track);
            }
          });
        } else if (track is AudioTrack) {
          // Audio tracks are typically managed automatically by LiveKit
        }
      } else if (event is TrackUnsubscribedEvent) {
        final track = event.track;
        final publication = event.publication;
        if (track is VideoTrack) {
          setState(() {
            if (publication.source == TrackSource.screenShareVideo ||
                publication.source == TrackSource.screenShareAudio) {
              screenShareTracks.remove(track);
            } else {
              cameraTracks.remove(track);
            }
          });
        } else if (track is AudioTrack) {
          // Audio tracks are typically managed automatically by LiveKit
        }
      }
    });
  }

  void _startChatPolling(String sessionId) {
    _chatPollTimer?.cancel();
    _loadChatHistory(sessionId);
    _chatPollTimer = Timer.periodic(const Duration(seconds: 2), (_) {
      if (currentStream != null) {
        _loadChatHistory(currentStream!.sessionId);
      }
    });
  }

  Future<void> _loadChatHistory(String sessionId) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    if (token == null) return;

    try {
      final response = await http.get(
        Uri.parse('https://testing.sasthra.in/api/chat/history/$sessionId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final messages = (data['messages'] as List)
            .map((m) => ChatMessage.fromJson(m))
            .toList();

        if (messages.length > chatMessages.length && !isChatOpen) {
          setState(() {
            unreadMessages = messages.length - chatMessages.length;
          });
        }

        setState(() {
          chatMessages = messages;
        });
      }
    } catch (e) {
      print('Error loading chat: $e');
    }
  }

  Future<void> _sendChatMessage() async {
    if (_messageController.text.trim().isEmpty || currentStream == null) return;

    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString('userId') ?? 'Unknown';
    final userName = prefs.getString('name') ?? 'Student';
    final token = prefs.getString('token');

    final messageData = {
      'session_id': currentStream!.sessionId,
      'message': _messageController.text.trim(),
      'sender_id': userId,
      'sender_name': userName,
    };

    setState(() {
      isSendingMessage = true;
    });

    try {
      await http.post(
        Uri.parse('https://testing.sasthra.in/api/chat/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(messageData),
      );

      _messageController.clear();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to send message')),
      );
    } finally {
      setState(() {
        isSendingMessage = false;
      });
    }
  }

  void _onLeaveStream() {
    _disconnectLiveKit();
    _chatPollTimer?.cancel();
    setState(() {
      isViewingStream = false;
      currentStream = null;
      livekitConnected = false;
      participants.clear();
      screenShareTracks.clear();
      cameraTracks.clear();
      chatMessages.clear();
      isChatOpen = false;
      unreadMessages = 0;
    });
  }

  void _disconnectLiveKit() {
    if (livekitRoom != null) {
      livekitRoom!.disconnect();
      livekitRoom = null;
    }
  }

  String formatUptime(int seconds) {
    final hours = (seconds ~/ 3600);
    final minutes = (seconds % 3600) ~/ 60;
    if (hours > 0) return '$hours h ${minutes}m';
    return '${minutes}m';
  }

  Widget _buildChatSidebar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isChatOpen ? 320 : 0,
      child: isChatOpen
          ? Container(
              color: Colors.white,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Colors.indigo,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Chat',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () {
                            setState(() {
                              isChatOpen = false;
                              unreadMessages = 0;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: chatMessages.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.message, size: 48, color: Colors.grey),
                                Text('No messages yet'),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(12),
                            itemCount: chatMessages.length,
                            itemBuilder: (ctx, i) {
                              final msg = chatMessages[i];
                              return Container(
                                margin: const EdgeInsets.symmetric(vertical: 4),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          msg.senderName,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.indigo,
                                          ),
                                        ),
                                        Text(
                                          _timeFormat.format(msg.timestamp),
                                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(msg.message),
                                  ],
                                ),
                              );
                            },
                          ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            onSubmitted: (_) => _sendChatMessage(),
                            decoration: const InputDecoration(
                              hintText: 'Type a message...',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: isSendingMessage ? null : _sendChatMessage,
                          icon: isSendingMessage
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.send, color: Colors.indigo),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isViewingStream && currentStream != null) {
      return Scaffold(
        body: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF1A237E),
                boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 6)],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _onLeaveStream,
                        icon: const Icon(Icons.arrow_back, size: 16),
                        label: const Text('Leave'),
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Live Stream',
                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'Session: ${currentStream!.sessionId}',
                            style: const TextStyle(color: Colors.white70, fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: livekitConnected ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          livekitConnected ? 'Connected' : 'Disconnected',
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            isChatOpen = !isChatOpen;
                            if (isChatOpen) unreadMessages = 0;
                          });
                        },
                        icon: const Icon(Icons.chat, size: 16),
                        label: const Text('Chat'),
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.indigo),
                      ),
                      if (unreadMessages > 0)
                        Container(
                          margin: const EdgeInsets.only(left: 4),
                          padding: const EdgeInsets.all(6),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '$unreadMessages',
                            style: const TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            // Video Area
            Expanded(
              child: Stack(
                children: [
                  // Main video
                  Container(
                    color: Colors.black,
                    child: screenShareTracks.isNotEmpty
                        ? VideoTrackRenderer(screenShareTracks.first)
                        : (cameraTracks.isNotEmpty
                            ? VideoTrackRenderer(cameraTracks.first)
                            : const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.videocam_off, size: 48, color: Colors.grey),
                                    Text(
                                      'Waiting for stream...',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ],
                                ),
                              )),
                  ),
                  // PIP Camera
                  if (screenShareTracks.isNotEmpty && cameraTracks.isNotEmpty)
                    Positioned(
                      bottom: 16,
                      right: 16,
                      width: 140,
                      height: 100,
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: VideoTrackRenderer(cameraTracks.first),
                      ),
                    ),
                ],
              ),
            ),
            // Chat Sidebar
            _buildChatSidebar(),
          ],
        ),
      );
    }

    // Main Dashboard: List Active Streams + Manual Join
    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streaming'),
        centerTitle: true,
        backgroundColor: Colors.indigo,
      ),
      body: RefreshIndicator(
        onRefresh: _loadActiveStreams,
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.all(16),
              sliver: SliverToBoxAdapter(
                child: Column(
                  children: [
                    const Text(
                      'Join Active Streams or Enter Session ID',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    // Manual Join
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Join Stream Manually',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.indigo,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _sessionIdController,
                                    decoration: const InputDecoration(
                                      hintText: 'Enter Session ID',
                                      border: OutlineInputBorder(),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                ElevatedButton(
                                  onPressed: isJoining
                                      ? null
                                      : () => _joinStreamById(_sessionIdController.text.trim()),
                                  child: isJoining
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(strokeWidth: 2),
                                        )
                                      : const Icon(Icons.play_arrow),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverToBoxAdapter(
                child: Text(
                  'Active Streams',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            isLoading && activeStreams.isEmpty
                ? const SliverFillRemaining(
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                : activeStreams.isEmpty
                    ? const SliverFillRemaining(
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.videocam_off, size: 64, color: Colors.grey),
                              Text('No active streams available'),
                            ],
                          ),
                        ),
                      )
                    : SliverGrid(
                        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                          maxCrossAxisExtent: 300,
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          childAspectRatio: 0.8,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final stream = activeStreams[index];
                            return Card(
                              elevation: 3,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Live Stream #${index + 1}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.indigo,
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.indigo[100],
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            stream.quality,
                                            style: const TextStyle(
                                              fontSize: 11,
                                              color: Colors.indigo,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      'Teacher: ${stream.teacherId}',
                                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                                    ),
                                    const SizedBox(height: 10),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        _buildStat('Viewers', stream.viewerCount.toString()),
                                        _buildStat('Uptime', formatUptime(stream.uptime)),
                                      ],
                                    ),
                                    const Spacer(),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow[100],
                                        border: Border.all(color: Colors.yellow[300]!),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Column(
                                        children: [
                                          Text(
                                            '🔒 Session ID Required',
                                            style: TextStyle(
                                              color: Colors.orange,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'Use manual join above',
                                            style: TextStyle(fontSize: 11, color: Colors.orange),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    ElevatedButton.icon(
                                      onPressed: () => _joinStreamById(stream.sessionId),
                                      icon: const Icon(Icons.play_arrow, size: 16),
                                      label: const Text('Join'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.indigo,
                                        padding: const EdgeInsets.symmetric(vertical: 4),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          childCount: activeStreams.length,
                        ),
                      ),
          ],
        ),
      ),
    );
  }

  Widget _buildStat(String label, String value) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.indigo,
              ),
            ),
            Text(
              label,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

class ActiveStream {
  final String sessionId;
  final String teacherId;
  final int viewerCount;
  final int uptime;
  final String quality;

  ActiveStream({
    required this.sessionId,
    required this.teacherId,
    required this.viewerCount,
    required this.uptime,
    this.quality = 'Standard',
  });

  factory ActiveStream.fromJson(Map<String, dynamic> json) {
    return ActiveStream(
      sessionId: json['session_id'],
      teacherId: json['teacher_id'] ?? 'Unknown',
      viewerCount: json['viewer_count'] ?? 0,
      uptime: json['uptime'] ?? 0,
      quality: json['quality'] ?? 'Standard',
    );
  }
}

class ChatMessage {
  final String senderId;
  final String senderName;
  final String message;
  final DateTime timestamp;

  ChatMessage({
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      senderId: json['sender_id'],
      senderName: json['sender_name'] ?? 'Unknown',
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}